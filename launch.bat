@echo off
title AI PC Shortcuts Manager Pro - Launcher
color 0A

echo.
echo ========================================
echo  AI PC Shortcuts Manager Pro v1.2.0
echo  Developed by AMSSoftX
echo  https://amssoftx.com
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo [INFO] Python found, checking version...
python -c "import sys; exit(0 if sys.version_info >= (3,8) else 1)" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python 3.8 or higher is required
    python --version
    echo Please upgrade Python and try again
    echo.
    pause
    exit /b 1
)

echo [INFO] Python version OK
echo [INFO] Launching AI PC Shortcuts Manager Pro...
echo.

REM Launch the application
python launch.py

if errorlevel 1 (
    echo.
    echo [ERROR] Application failed to start
    echo Check the error messages above for details
    echo.
    pause
)

echo.
echo Application closed.
pause
