#!/usr/bin/env python3
"""
Launcher script for AI PC Shortcuts Manager Pro
This script handles dependency checking and provides a clean entry point.
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required.")
        print(f"Current version: {sys.version}")
        print("Please upgrade Python and try again.")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = {
        'keyboard': 'keyboard>=1.13.0',
        'psutil': 'psutil>=5.9.0', 
        'requests': 'requests>=2.28.0'
    }
    
    missing_packages = []
    
    for package, requirement in required_packages.items():
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(requirement)
    
    if missing_packages:
        print("❌ Missing required dependencies:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n📦 To install missing dependencies, run:")
        print("   pip install " + " ".join(missing_packages))
        print("\nOr install all requirements:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main launcher function."""
    print("🚀 AI PC Shortcuts Manager Pro v1.2.0")
    print("   Developed by AMSSoftX")
    print("   https://amssoftx.com")
    print("-" * 50)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return 1
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    if not check_dependencies():
        print("\n❓ Would you like to install missing dependencies automatically? (y/n): ", end="")
        try:
            response = input().lower().strip()
            if response in ['y', 'yes']:
                print("📦 Installing dependencies...")
                try:
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
                    print("✅ Dependencies installed successfully!")
                except subprocess.CalledProcessError:
                    print("❌ Failed to install dependencies automatically.")
                    print("Please install them manually and try again.")
                    input("Press Enter to exit...")
                    return 1
            else:
                print("Installation cancelled.")
                input("Press Enter to exit...")
                return 1
        except KeyboardInterrupt:
            print("\nInstallation cancelled.")
            return 1
    
    # Launch the main application
    print("✅ All dependencies satisfied!")
    print("🎯 Launching AI PC Shortcuts Manager Pro...")
    
    try:
        # Import and run the main application
        from AI_PC_Shortcuts_Manager_Pro_V1_1 import ModernAIShortcutsManager
        
        app = ModernAIShortcutsManager()
        app.run()
        
    except ImportError as e:
        print(f"❌ Error importing main application: {e}")
        print("Please ensure AI_PC_Shortcuts_Manager_Pro_V1.1.py is in the same directory.")
        input("Press Enter to exit...")
        return 1
    except Exception as e:
        print(f"❌ Error launching application: {e}")
        print("Please check the error log for more details.")
        input("Press Enter to exit...")
        return 1
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        input("Press Enter to exit...")
        sys.exit(1)
