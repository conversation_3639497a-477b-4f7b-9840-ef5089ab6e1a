# AI PC Shortcuts Manager Pro v2.0.0

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Python](https://img.shields.io/badge/python-3.8+-green.svg)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)
![AI](https://img.shields.io/badge/AI-Powered-brightgreen.svg)

**Revolutionary AI-powered shortcut management with machine learning capabilities**

Developed by **AMSSoftX** | [https://amssoftx.com](https://amssoftx.com)

## 🤖 AI-Powered Features

### Advanced AI Intelligence
- **🧠 Machine Learning Engine** - Real-time learning from your usage patterns
- **🎯 Smart Predictions** - AI predicts optimal categories and suggestions
- **📊 Behavioral Analysis** - Understands your workflow and preferences
- **🔍 Intelligent Cleanup** - AI identifies redundant and unused shortcuts
- **⚡ Auto-Optimization** - Suggests improvements for better organization
- **🎨 Pattern Recognition** - Learns your naming conventions and styles

### Core Features
- **Modern Dark UI** - Professional, eye-friendly interface
- **Global Hotkeys** - Quick access with keyboard combinations
- **Multi-Category Support** - Applications, Websites, Files, Folders, System commands
- **Advanced Analytics** - AI-enhanced usage tracking and insights
- **Multi-Language Support** - English, Hindi, Marathi
- **Data Security** - Enhanced validation and integrity checking
- **Import/Export** - Easy data migration and backup
- **Real-time Learning** - AI continuously improves suggestions

## 📦 Installation

### Quick Install
```bash
# Clone the repository
git clone https://github.com/amssoftx/ai-shortcuts-manager.git
cd ai-shortcuts-manager

# Install dependencies
pip install -r requirements.txt

# Run the application
python AI_PC_Shortcuts_Manager_Pro_V1.1.py
```

### Using Setup Script
```bash
# Install as a package
pip install -e .

# Run from anywhere
ai-shortcuts
```

## 🔧 Requirements

- **Python 3.8+**
- **Operating System**: Windows 10+, Linux, macOS
- **RAM**: 512MB minimum, 1GB recommended
- **Storage**: 100MB free space

### Dependencies
- `keyboard>=1.13.0` - Global hotkey support
- `psutil>=5.9.0` - System monitoring
- `requests>=2.28.0` - Network features
- `openai>=1.0.0` - AI language processing
- `nltk>=3.8` - Natural language toolkit
- `scikit-learn>=1.3.0` - Machine learning algorithms
- `numpy>=1.24.0` - Numerical computing

## 🎯 Quick Start

1. **Launch the application**
2. **Add your first shortcut**:
   - Click "⚡ Quick Add" or "➕ Add"
   - Enter name and target (file/URL/command)
   - Optionally set a hotkey
   - Choose category and add description
3. **Use AI suggestions** - Visit the "🧠 AI Assistant" tab
4. **View analytics** - Check "📈 Analytics" for usage insights

## 📚 User Guide

### Adding Shortcuts
- **Applications**: Browse to .exe files or enter command names
- **Websites**: Enter full URLs (https://example.com)
- **Files/Folders**: Use browse buttons or enter paths
- **System Commands**: Enter command-line instructions

### Hotkeys
- Use combinations like `ctrl+alt+s`, `shift+f1`
- Avoid system hotkeys to prevent conflicts
- Global hotkeys work system-wide when enabled

### AI Intelligence Center
- **🤖 Smart Analysis**: Comprehensive AI analysis of usage patterns
- **🧹 AI Cleanup**: Intelligent cleanup with machine learning
- **🎯 Optimize Shortcuts**: AI-powered organization suggestions
- **📊 Behavioral Learning**: Real-time pattern recognition
- **🔍 Predictive Suggestions**: AI predicts what you need next
- **⚡ Auto-Categorization**: Smart category prediction for new shortcuts

## 🔒 Security Features

- **Input Validation** - Prevents malicious entries
- **Security Hashing** - Detects target modifications
- **System Command Filtering** - Blocks dangerous commands
- **Error Logging** - Tracks issues for debugging

## 📊 Analytics & Reporting

- **Usage Statistics** - Track shortcut usage
- **Daily Trends** - Visual usage patterns
- **Performance Metrics** - App performance monitoring
- **Export Reports** - Generate detailed usage reports

## 🌐 Multi-Language Support

Currently supported languages:
- **English** (Default)
- **Hindi** (हिंदी)
- **Marathi** (मराठी)

## 🛠️ Development

### Project Structure
```
ai-shortcuts-manager/
├── AI_PC_Shortcuts_Manager_Pro_V1.1.py  # Main application
├── requirements.txt                      # Dependencies
├── setup.py                             # Installation script
├── README.md                            # Documentation
└── icon.ico                            # Application icon
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Website**: [https://amssoftx.com](https://amssoftx.com)
- **Email**: <EMAIL>
- **Documentation**: [https://amssoftx.com/docs/ai-shortcuts-manager](https://amssoftx.com/docs/ai-shortcuts-manager)

## 🔄 Changelog

### v2.0.0 (Latest) - AI Revolution
- 🤖 **Revolutionary AI Engine** - Machine learning-powered intelligence
- 🧠 **Real-time Learning** - AI learns from your usage patterns
- 🎯 **Smart Predictions** - AI predicts categories and suggests optimizations
- 📊 **Behavioral Analysis** - Advanced pattern recognition
- 🔍 **Intelligent Cleanup** - AI-powered duplicate detection and cleanup
- ⚡ **Auto-Optimization** - Smart suggestions for better organization
- 🎨 **Enhanced UI** - AI status indicators and intelligence center
- 🚀 **Performance Boost** - Optimized for speed and efficiency
- 🔒 **Enhanced Security** - Advanced validation and integrity checking
- 🌟 **Removed Premium Restrictions** - All features now free!

### v1.2.0
- ✨ Enhanced security features
- ✨ Premium licensing system
- ✨ Improved AI suggestions
- ✨ Better error handling
- ✨ Performance monitoring
- 🐛 Fixed various bugs
- 🎨 UI improvements

### v1.1.0
- ✨ AI-powered suggestions
- ✨ Multi-language support
- ✨ Advanced analytics
- ✨ Global hotkeys
- ✨ Import/export functionality

## 🏆 Why Choose AI Shortcuts Manager Pro?

- **Professional Grade** - Built for power users and professionals
- **AI-Enhanced** - Smart suggestions save time
- **Cross-Platform** - Works on Windows, Linux, and macOS
- **Secure** - Enterprise-level security features
- **Extensible** - Plugin architecture for custom features
- **Supported** - Regular updates and professional support

---

**Made with ❤️ by AMSSoftX**

*Streamline your workflow with intelligent shortcut management*
