#!/usr/bin/env python3
"""
Setup script for AI PC Shortcuts Manager Pro
"""

from setuptools import setup, find_packages
import sys
import os

# Read the README file
def read_readme():
    try:
        with open("README.md", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return "AI PC Shortcuts Manager Pro - Professional shortcut management application"

# Read requirements
def read_requirements():
    try:
        with open("requirements.txt", "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    except FileNotFoundError:
        return ["keyboard>=1.13.0", "psutil>=5.9.0", "requests>=2.28.0"]

setup(
    name="ai-pc-shortcuts-manager-pro",
    version="1.2.0",
    author="AMSSoftX",
    author_email="<EMAIL>",
    description="Professional AI-powered shortcut management application",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://amssoftx.com",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Desktop Environment",
        "Topic :: System :: System Shells",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=5.0.0",
        ],
        "advanced": [
            "matplotlib>=3.6.0",
            "pandas>=1.5.0",
            "customtkinter>=5.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "ai-shortcuts=AI_PC_Shortcuts_Manager_Pro_V1.1:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.ico", "*.png", "*.jpg", "*.txt", "*.md"],
    },
    keywords="shortcuts, productivity, automation, AI, desktop, management",
    project_urls={
        "Bug Reports": "https://github.com/amssoftx/ai-shortcuts-manager/issues",
        "Source": "https://github.com/amssoftx/ai-shortcuts-manager",
        "Documentation": "https://amssoftx.com/docs/ai-shortcuts-manager",
    },
)
